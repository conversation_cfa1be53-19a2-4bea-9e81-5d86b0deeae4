<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/auth.php';

// 检查用户是否登录
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$currentUser = $auth->getCurrentUser();

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// 获取用户的帖子
try {
    $db = db();
    
    // 获取总数
    $totalSql = "SELECT COUNT(*) as total FROM posts WHERE user_id = ? AND status IN ('published', 'draft')";
    $totalResult = $db->fetchOne($totalSql, [$currentUser['id']]);
    $total = $totalResult['total'];
    $totalPages = ceil($total / $limit);
    
    // 获取帖子列表
    $sql = "SELECT p.*, pc.name as category_name,
                   (SELECT COUNT(*) FROM post_comments WHERE post_id = p.id AND status = 'approved') as comment_count,
                   (SELECT COUNT(*) FROM post_likes WHERE post_id = p.id AND type = 'like') as like_count,
                   (SELECT COUNT(*) FROM post_likes WHERE post_id = p.id AND type = 'dislike') as dislike_count
            FROM posts p
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            WHERE p.user_id = ? AND p.status IN ('published', 'draft')
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?";
    
    $posts = $db->fetchAll($sql, [$currentUser['id'], $limit, $offset]);
    
} catch (Exception $e) {
    $error = '获取帖子列表失败：' . $e->getMessage();
    $posts = [];
    $total = 0;
    $totalPages = 0;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的帖子 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .post-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .post-status.published {
            background: #dcfce7;
            color: #16a34a;
        }
        
        .post-status.draft {
            background: #fef3c7;
            color: #d97706;
        }
        
        .post-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }
        
        .post-stats {
            display: flex;
            gap: 16px;
            margin-top: 8px;
            font-size: 14px;
            color: #6b7280;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d1d5db;
        }
        
        .empty-state h3 {
            margin-bottom: 8px;
            color: #374151;
        }

        /* 页面头部美化 */
        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .header-info h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-info p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        /* 帖子卡片美化 */
        .post-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
        }

        .post-title {
            color: #1f2937 !important;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php" class="brand-link">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>

            <div class="nav-menu">
                <a href="user-profile.php" class="nav-link">
                    <i class="fas fa-user"></i> 个人资料
                </a>
                <a href="community.php" class="nav-link">
                    <i class="fas fa-comments"></i> 社区
                </a>
                <a href="my-posts.php" class="nav-link active">
                    <i class="fas fa-file-alt"></i> 我的帖子
                </a>
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i> 通知中心
                </a>
                <?php if ($auth->hasRole('admin') || $auth->hasRole('super_admin')): ?>
                    <a href="admin" class="nav-link">
                        <i class="fas fa-cog"></i> 管理后台
                    </a>
                <?php endif; ?>
            </div>

            <div class="nav-user">
                <?php if ($currentUser): ?>
                    <div class="user-profile-card">
                        <img src="<?php echo htmlspecialchars($currentUser['avatar'] ?? 'assets/images/default-avatar.png'); ?>"
                             alt="用户头像" class="user-avatar-small">
                        <span class="user-name-small"><?php echo htmlspecialchars($currentUser['nickname'] ?? $currentUser['username']); ?></span>
                    </div>
                <?php else: ?>
                    <div class="auth-buttons">
                        <a href="login.php" class="btn btn-outline btn-small">登录</a>
                        <a href="register.php" class="btn btn-primary btn-small">注册</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="header-content">
                    <div class="header-info">
                        <h1>我的帖子</h1>
                        <p>管理您发布的所有帖子，包括已发布和草稿</p>
                    </div>
                    <div class="header-actions">
                        <a href="community-post.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            发布新帖
                        </a>
                        <a href="community-drafts.php" class="btn btn-secondary">
                            <i class="fas fa-file-alt"></i>
                            草稿箱
                        </a>
                    </div>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- 帖子列表 -->
            <div class="posts-grid">
                <?php if (empty($posts)): ?>
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <h3>还没有发布任何帖子</h3>
                        <p>开始创作您的第一篇帖子吧！</p>
                        <a href="community-post.php" class="btn btn-primary" style="margin-top: 16px;">
                            <i class="fas fa-plus"></i>
                            发布新帖
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($posts as $post): ?>
                        <article class="post-card">
                            <div class="post-header">
                                <div class="post-meta">
                                    <span class="post-status <?php echo $post['status']; ?>">
                                        <i class="fas fa-<?php echo $post['status'] === 'published' ? 'check-circle' : 'edit'; ?>"></i>
                                        <?php echo $post['status'] === 'published' ? '已发布' : '草稿'; ?>
                                    </span>
                                    <?php if ($post['category_name']): ?>
                                        <span class="post-category"><?php echo htmlspecialchars($post['category_name']); ?></span>
                                    <?php endif; ?>
                                </div>
                                <span class="post-date"><?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?></span>
                            </div>
                            
                            <h2 class="post-title">
                                <a href="community-post-detail.php?id=<?php echo $post['id']; ?>">
                                    <?php echo htmlspecialchars($post['title']); ?>
                                </a>
                            </h2>
                            
                            <div class="post-excerpt">
                                <?php echo htmlspecialchars(mb_substr($post['excerpt'] ?: strip_tags($post['content']), 0, 150)); ?>
                            </div>
                            
                            <div class="post-stats">
                                <div class="stat-item">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span><?php echo $post['like_count']; ?></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-thumbs-down"></i>
                                    <span><?php echo $post['dislike_count']; ?></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-comments"></i>
                                    <span><?php echo $post['comment_count']; ?></span>
                                </div>
                            </div>
                            
                            <div class="post-actions">
                                <a href="community-post-detail.php?id=<?php echo $post['id']; ?>" class="btn btn-outline btn-small">
                                    <i class="fas fa-eye"></i> 查看
                                </a>
                                <a href="community-post.php?edit=<?php echo $post['id']; ?>" class="btn btn-primary btn-small">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <button onclick="deletePost(<?php echo $post['id']; ?>)" class="btn btn-danger btn-small">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </article>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>" class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </a>
                    <?php endif; ?>
                    
                    <span class="pagination-info">
                        第 <?php echo $page; ?> 页，共 <?php echo $totalPages; ?> 页
                    </span>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?php echo $page + 1; ?>" class="pagination-btn">
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        // 删除帖子
        function deletePost(postId) {
            if (confirm('确定要删除这篇帖子吗？此操作不可恢复。')) {
                fetch('api/post-actions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        post_id: postId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('帖子删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    alert('删除失败，请稍后重试');
                });
            }
        }
    </script>
</body>
</html>
