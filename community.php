<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'includes/time_helper.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 15;
$offset = ($page - 1) * $limit;

// 获取排序参数
$sort = $_GET['sort'] ?? 'latest';
$category = $_GET['category'] ?? '';

// 构建排序条件
$orderBy = match($sort) {
    'popular' => 'p.like_count DESC, p.view_count DESC',
    'discussed' => 'p.comment_count DESC',
    'oldest' => 'p.created_at ASC',
    default => 'p.is_pinned DESC, p.created_at DESC'
};

try {
    $db = db();
    
    // 构建查询条件
    $whereClause = "p.status = 'published'";
    $params = [];
    
    if ($category) {
        $whereClause .= " AND pc.slug = ?";
        $params[] = $category;
    }
    
    // 获取帖子列表
    $sql = "SELECT p.*, u.username, u.full_name, up.nickname, up.avatar_url,
                   pc.name as category_name, pc.slug as category_slug, pc.color as category_color
            FROM posts p
            LEFT JOIN users u ON p.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            WHERE {$whereClause}
            ORDER BY {$orderBy}
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $posts = $db->fetchAll($sql, $params);
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total
                 FROM posts p
                 LEFT JOIN post_categories pc ON p.category_id = pc.id
                 WHERE {$whereClause}";
    
    $totalPosts = $db->fetchOne($countSql, array_slice($params, 0, -2))['total'];
    $totalPages = ceil($totalPosts / $limit);
    
    // 获取分类列表
    $categories = $db->fetchAll("SELECT * FROM post_categories WHERE is_active = 1 ORDER BY sort_order");
    
} catch (Exception $e) {
    error_log("社区页面错误: " . $e->getMessage());
    $posts = [];
    $categories = [];
    $totalPosts = 0;
    $totalPages = 1;
}

// 辅助函数已移至 includes/time_helper.php
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link active">社区</a>
                <a href="#" class="nav-link">课程</a>
                <a href="#" class="nav-link">文档</a>
            </div>
            
            <div class="nav-actions">
                <?php if ($currentUser): ?>
                    <a href="community-post.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 发帖
                    </a>
                    <a href="bookmarks.php" class="nav-link" title="我的收藏">
                        <i class="fas fa-bookmark"></i>
                    </a>
                    <div class="notification-icon" id="notificationIcon" title="通知">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                    </div>
                    <div class="user-menu">
                        <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>"
                             alt="头像" class="user-avatar">
                        <span><?php echo escapeHtml($currentUser['username']); ?></span>
                    </div>
                <?php else: ?>
                    <a href="admin-login.php" class="btn btn-outline">登录</a>
                    <a href="register.php" class="btn btn-primary">注册</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 社区头部 -->
            <div class="community-header">
                <div class="header-content">
                    <h1 class="community-title">技术社区</h1>
                    <p class="community-description">分享知识，交流经验，共同成长</p>
                </div>
                
                <?php if ($currentUser): ?>
                <div class="header-actions">
                    <a href="community-post.php" class="btn btn-primary btn-large">
                        <i class="fas fa-plus"></i> 发布新帖
                    </a>
                    <a href="community-drafts.php" class="btn btn-secondary btn-large">
                        <i class="fas fa-file-alt"></i> 草稿
                        <span class="draft-count" id="draftCount" style="display: none;">0</span>
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <!-- 过滤和排序 -->
            <div class="filters-bar">
                <div class="categories-filter">
                    <a href="community.php" class="category-tag <?php echo !$category ? 'active' : ''; ?>">
                        全部
                    </a>
                    <?php foreach ($categories as $cat): ?>
                    <a href="community.php?category=<?php echo $cat['slug']; ?>" 
                       class="category-tag <?php echo $category === $cat['slug'] ? 'active' : ''; ?>"
                       style="--category-color: <?php echo $cat['color']; ?>">
                        <?php if ($cat['icon']): ?>
                            <span class="category-icon"><?php echo $cat['icon']; ?></span>
                        <?php endif; ?>
                        <?php echo escapeHtml($cat['name']); ?>
                    </a>
                    <?php endforeach; ?>
                </div>
                
                <div class="sort-filter">
                    <select id="sortSelect" onchange="changeSortOrder(this.value)">
                        <option value="latest" <?php echo $sort === 'latest' ? 'selected' : ''; ?>>最新发布</option>
                        <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>最受欢迎</option>
                        <option value="discussed" <?php echo $sort === 'discussed' ? 'selected' : ''; ?>>最多讨论</option>
                        <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>最早发布</option>
                    </select>
                </div>
            </div>

            <!-- 帖子列表 -->
            <div class="posts-container">
                <?php if (empty($posts)): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>暂无帖子</h3>
                    <p>成为第一个发帖的人吧！</p>
                    <?php if ($currentUser): ?>
                    <a href="community-post.php" class="btn btn-primary">发布第一个帖子</a>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="posts-list">
                    <?php foreach ($posts as $post): ?>
                    <article class="post-card <?php echo $post['is_pinned'] ? 'pinned' : ''; ?>">
                        <?php if ($post['is_pinned']): ?>
                        <div class="pin-indicator">
                            <i class="fas fa-thumbtack"></i> 置顶
                        </div>
                        <?php endif; ?>
                        
                        <div class="post-header">
                            <div class="author-info">
                                <a href="user-profile.php?id=<?php echo $post['user_id']; ?>" class="author-avatar-link">
                                    <img src="<?php echo $post['avatar_url'] ?? 'assets/images/default-avatar.png'; ?>"
                                         alt="头像" class="author-avatar">
                                </a>
                                <div class="author-details">
                                    <a href="user-profile.php?id=<?php echo $post['user_id']; ?>" class="author-name-link">
                                        <span class="author-name">
                                            <?php echo escapeHtml($post['nickname'] ?? $post['full_name'] ?? $post['username']); ?>
                                        </span>
                                    </a>
                                    <span class="post-time"><?php echo timeAgo($post['created_at']); ?></span>
                                </div>
                            </div>
                            
                            <?php if ($post['category_name']): ?>
                            <div class="post-category" style="background-color: <?php echo $post['category_color']; ?>">
                                <?php echo escapeHtml($post['category_name']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="post-content">
                            <h2 class="post-title">
                                <a href="community-post-detail.php?id=<?php echo $post['id']; ?>">
                                    <?php echo escapeHtml($post['title']); ?>
                                </a>
                            </h2>
                            
                            <?php if ($post['excerpt']): ?>
                            <p class="post-excerpt"><?php echo escapeHtml($post['excerpt']); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($post['featured_image']): ?>
                            <div class="post-image">
                                <img src="<?php echo $post['featured_image']; ?>" alt="帖子图片">
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="post-stats">
                            <div class="stat-item">
                                <i class="fas fa-eye"></i>
                                <span><?php echo formatNumber($post['view_count']); ?></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-heart"></i>
                                <span><?php echo formatNumber($post['like_count']); ?></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-comment"></i>
                                <span><?php echo formatNumber($post['comment_count']); ?></span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-share"></i>
                                <span><?php echo formatNumber($post['share_count']); ?></span>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&sort=<?php echo $sort; ?>&category=<?php echo $category; ?>" 
                   class="page-btn">
                    <i class="fas fa-chevron-left"></i> 上一页
                </a>
                <?php endif; ?>
                
                <div class="page-numbers">
                    <?php
                    $start = max(1, $page - 2);
                    $end = min($totalPages, $page + 2);
                    
                    if ($start > 1): ?>
                        <a href="?page=1&sort=<?php echo $sort; ?>&category=<?php echo $category; ?>" class="page-num">1</a>
                        <?php if ($start > 2): ?>
                            <span class="page-dots">...</span>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php for ($i = $start; $i <= $end; $i++): ?>
                        <a href="?page=<?php echo $i; ?>&sort=<?php echo $sort; ?>&category=<?php echo $category; ?>" 
                           class="page-num <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($end < $totalPages): ?>
                        <?php if ($end < $totalPages - 1): ?>
                            <span class="page-dots">...</span>
                        <?php endif; ?>
                        <a href="?page=<?php echo $totalPages; ?>&sort=<?php echo $sort; ?>&category=<?php echo $category; ?>" 
                           class="page-num"><?php echo $totalPages; ?></a>
                    <?php endif; ?>
                </div>
                
                <?php if ($page < $totalPages): ?>
                <a href="?page=<?php echo $page + 1; ?>&sort=<?php echo $sort; ?>&category=<?php echo $category; ?>" 
                   class="page-btn">
                    下一页 <i class="fas fa-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        function changeSortOrder(sort) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sort);
            url.searchParams.set('page', '1'); // 重置到第一页
            window.location.href = url.toString();
        }

        // 全局会话刷新函数
        window.refreshUserSession = function() {
            return fetch('api/refresh-session.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新页面中的用户头像和昵称
                        const userAvatar = document.querySelector('.user-avatar');
                        const userNickname = document.querySelector('.user-menu span');

                        if (userAvatar && data.user.avatar) {
                            userAvatar.src = data.user.avatar;
                        }

                        if (userNickname && data.user.nickname) {
                            userNickname.textContent = data.user.nickname;
                        }

                        console.log('用户会话已刷新');
                        return data;
                    } else {
                        console.error('刷新会话失败:', data.message);
                        return data;
                    }
                })
                .catch(error => {
                    console.error('刷新会话请求失败:', error);
                    return { success: false, error: error.message };
                });
        };

        // 通知功能
        function loadNotificationCount() {
            <?php if ($currentUser): ?>
            fetch('api/notifications.php?action=count')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.count > 0) {
                        document.getElementById('notificationBadge').textContent = data.count;
                        document.getElementById('notificationBadge').style.display = 'flex';
                    } else {
                        document.getElementById('notificationBadge').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('加载通知数量失败:', error);
                });
            <?php endif; ?>
        }

        // 点击通知图标
        document.getElementById('notificationIcon')?.addEventListener('click', function() {
            // 这里可以打开通知列表或跳转到通知页面
            alert('通知功能正在开发中...');
        });

        // 加载草稿数量
        function loadDraftCount() {
            <?php if ($currentUser): ?>
            fetch('api/community-drafts.php?action=count')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.count > 0) {
                        const draftCountElement = document.getElementById('draftCount');
                        if (draftCountElement) {
                            draftCountElement.textContent = data.count;
                            draftCountElement.style.display = 'flex';
                        }
                    }
                })
                .catch(error => {
                    console.error('加载草稿数量失败:', error);
                });
            <?php endif; ?>
        }

        // 页面加载时获取通知数量和草稿数量
        document.addEventListener('DOMContentLoaded', function() {
            loadNotificationCount();
            loadDraftCount();
            // 每30秒刷新一次通知数量
            setInterval(loadNotificationCount, 30000);
        });
    </script>
</body>
</html>
